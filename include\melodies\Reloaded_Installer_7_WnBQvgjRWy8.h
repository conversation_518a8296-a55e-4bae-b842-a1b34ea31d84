// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #7 [WnBQvgjRWy8].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_7_WnBQvgjRWy8_note_count = 50;
constexpr int Reloaded_Installer_7_WnBQvgjRWy8_frequencies[] = {
    0, 862, 434, 181, 154, 157, 159, 1737, 
    282, 153, 177, 182, 213, 160, 211, 854, 
    178, 160, 159, 154, 210, 160, 159, 178, 
    177, 161, 157, 567, 538, 160, 159, 159, 
    159, 282, 159, 159, 180, 285, 159, 645, 
    212, 177, 159, 158, 157, 429, 282, 159, 
    158, 177
};
constexpr int Reloaded_Installer_7_WnBQvgjRWy8_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_7_WnBQvgjRWy8() {
    for (int i = 0; i < Reloaded_Installer_7_WnBQvgjRWy8_note_count; i++) {
        if (Reloaded_Installer_7_WnBQvgjRWy8_frequencies[i] > 0) {
            beep(Reloaded_Installer_7_WnBQvgjRWy8_frequencies[i], Reloaded_Installer_7_WnBQvgjRWy8_durations[i]);
        } else {
            delay(Reloaded_Installer_7_WnBQvgjRWy8_durations[i]); // Rest (silence)
        }
    }
}
