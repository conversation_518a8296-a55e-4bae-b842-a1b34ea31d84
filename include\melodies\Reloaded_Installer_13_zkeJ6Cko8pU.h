// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #13 [zkeJ6Cko8pU].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_13_zkeJ6Cko8pU_note_count = 50;
constexpr int Reloaded_Installer_13_zkeJ6Cko8pU_frequencies[] = {
    0, 852, 176, 196, 701, 1252, 174, 623, 
    834, 631, 707, 624, 938, 527, 516, 196, 
    692, 697, 847, 174, 1575, 196, 530, 790, 
    624, 196, 627, 693, 234, 936, 700, 517, 
    789, 704, 174, 174, 1177, 398, 393, 349, 
    352, 400, 392, 155, 470, 624, 526, 590, 
    595, 623
};
constexpr int Reloaded_Installer_13_zkeJ6Cko8pU_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_13_zkeJ6Cko8pU() {
    for (int i = 0; i < Reloaded_Installer_13_zkeJ6Cko8pU_note_count; i++) {
        if (Reloaded_Installer_13_zkeJ6Cko8pU_frequencies[i] > 0) {
            beep(Reloaded_Installer_13_zkeJ6Cko8pU_frequencies[i], Reloaded_Installer_13_zkeJ6Cko8pU_durations[i]);
        } else {
            delay(Reloaded_Installer_13_zkeJ6Cko8pU_durations[i]); // Rest (silence)
        }
    }
}
