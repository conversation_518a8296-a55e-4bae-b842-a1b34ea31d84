// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #14 [xKVjR5wJZK8].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_14_xKVjR5wJZK8_note_count = 50;
constexpr int Reloaded_Installer_14_xKVjR5wJZK8_frequencies[] = {
    0, 680, 762, 340, 225, 423, 679, 634, 
    251, 251, 423, 905, 752, 214, 908, 857, 
    768, 758, 676, 282, 566, 333, 505, 214, 
    857, 1019, 1010, 1131, 678, 252, 225, 516, 
    504, 764, 640, 251, 682, 187, 341, 225, 
    338, 650, 336, 644, 251, 428, 190, 657, 
    224, 224
};
constexpr int Reloaded_Installer_14_xKVjR5wJZK8_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_14_xKVjR5wJZK8() {
    for (int i = 0; i < Reloaded_Installer_14_xKVjR5wJZK8_note_count; i++) {
        if (Reloaded_Installer_14_xKVjR5wJZK8_frequencies[i] > 0) {
            beep(Reloaded_Installer_14_xKVjR5wJZK8_frequencies[i], Reloaded_Installer_14_xKVjR5wJZK8_durations[i]);
        } else {
            delay(Reloaded_Installer_14_xKVjR5wJZK8_durations[i]); // Rest (silence)
        }
    }
}
