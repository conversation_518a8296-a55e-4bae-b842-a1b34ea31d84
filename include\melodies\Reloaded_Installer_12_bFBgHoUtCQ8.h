// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #12 [bFBgHoUtCQ8].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_12_bFBgHoUtCQ8_note_count = 50;
constexpr int Reloaded_Installer_12_bFBgHoUtCQ8_frequencies[] = {
    0, 419, 156, 351, 207, 155, 235, 310, 
    305, 1046, 261, 526, 207, 395, 312, 154, 
    236, 149, 351, 155, 352, 208, 396, 234, 
    310, 262, 353, 262, 351, 208, 415, 312, 
    154, 234, 155, 351, 156, 394, 467, 394, 
    394, 155, 154, 174, 261, 418, 207, 624, 
    468, 624
};
constexpr int Reloaded_Installer_12_bFBgHoUtCQ8_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_12_bFBgHoUtCQ8() {
    for (int i = 0; i < Reloaded_Installer_12_bFBgHoUtCQ8_note_count; i++) {
        if (Reloaded_Installer_12_bFBgHoUtCQ8_frequencies[i] > 0) {
            beep(Reloaded_Installer_12_bFBgHoUtCQ8_frequencies[i], Reloaded_Installer_12_bFBgHoUtCQ8_durations[i]);
        } else {
            delay(Reloaded_Installer_12_bFBgHoUtCQ8_durations[i]); // Rest (silence)
        }
    }
}
