// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

#include "my_io.h"

/*
Converted from Reloaded installer #1 [4Yn174EAEwk].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_installer_1_4Yn174EAEwk_note_count = 50;
constexpr int Reloaded_installer_1_4Yn174EAEwk_frequencies[] = {
    0, 285, 853, 212, 504, 570, 697, 570, 
    428, 224, 857, 907, 764, 764, 636, 670, 
    857, 250, 569, 675, 282, 643, 571, 677, 
    213, 430, 571, 169, 907, 763, 763, 189, 
    251, 906, 857, 572, 573, 580, 282, 282, 
    282, 862, 217, 453, 452, 863, 224, 225, 
    252, 251
};
constexpr int Reloaded_installer_1_4Yn174EAEwk_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_installer_1_4Yn174EAEwk() {
    for (int i = 0; i < Reloaded_installer_1_4Yn174EAEwk_note_count; i++) {
        if (Reloaded_installer_1_4Yn174EAEwk_frequencies[i] > 0) {
            beep(Reloaded_installer_1_4Yn174EAEwk_frequencies[i], Reloaded_installer_1_4Yn174EAEwk_durations[i]);
        } else {
            delay(Reloaded_installer_1_4Yn174EAEwk_durations[i]); // Rest (silence)
        }
    }
}
