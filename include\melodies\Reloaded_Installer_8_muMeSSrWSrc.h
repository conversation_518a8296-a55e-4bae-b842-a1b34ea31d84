// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #8 [muMeSSrWSrc].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_8_muMeSSrWSrc_note_count = 50;
constexpr int Reloaded_Installer_8_muMeSSrWSrc_frequencies[] = {
    0, 572, 720, 956, 764, 859, 722, 648, 
    572, 577, 571, 481, 540, 540, 570, 414, 
    644, 575, 572, 644, 959, 1081, 1145, 1286, 
    867, 572, 579, 569, 478, 859, 644, 722, 
    1143, 1082, 429, 1142, 178, 190, 189, 188, 
    212, 212, 538, 1076, 571, 720, 957, 186, 
    539, 212
};
constexpr int Reloaded_Installer_8_muMeSSrWSrc_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_8_muMeSSrWSrc() {
    for (int i = 0; i < Reloaded_Installer_8_muMeSSrWSrc_note_count; i++) {
        if (Reloaded_Installer_8_muMeSSrWSrc_frequencies[i] > 0) {
            beep(Reloaded_Installer_8_muMeSSrWSrc_frequencies[i], Reloaded_Installer_8_muMeSSrWSrc_durations[i]);
        } else {
            delay(Reloaded_Installer_8_muMeSSrWSrc_durations[i]); // Rest (silence)
        }
    }
}
