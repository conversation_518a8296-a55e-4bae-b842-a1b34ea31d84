// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #3 [7VkqerXVtTA].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_3_7VkqerXVtTA_note_count = 50;
constexpr int Reloaded_Installer_3_7VkqerXVtTA_frequencies[] = {
    0, 179, 179, 721, 572, 642, 643, 481, 
    541, 179, 179, 179, 722, 573, 159, 160, 
    765, 159, 366, 359, 482, 361, 430, 429, 
    483, 159, 361, 179, 569, 572, 322, 159, 
    357, 481, 364, 365, 359, 482, 363, 430, 
    429, 481, 159, 361, 180, 569, 572, 361, 
    269, 321
};
constexpr int Reloaded_Installer_3_7VkqerXVtTA_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_3_7VkqerXVtTA() {
    for (int i = 0; i < Reloaded_Installer_3_7VkqerXVtTA_note_count; i++) {
        if (Reloaded_Installer_3_7VkqerXVtTA_frequencies[i] > 0) {
            beep(Reloaded_Installer_3_7VkqerXVtTA_frequencies[i], Reloaded_Installer_3_7VkqerXVtTA_durations[i]);
        } else {
            delay(Reloaded_Installer_3_7VkqerXVtTA_durations[i]); // Rest (silence)
        }
    }
}
