#!/usr/bin/env python3
"""
Process a single MP3 file for testing.
"""

from pathlib import Path
from .core import process_audio_file

def process_single_file():
    """Process a single MP3 file."""
    src_folder = Path("data-unprocessed")
    dst_folder = Path("include/melodies")
    
    # Process the first file
    file_path = src_folder / "Reloaded installer #1 [4Yn174EAEwk].mp3"
    
    if not file_path.exists():
        print(f"File not found: {file_path}")
        return
    
    dst_folder.mkdir(parents=True, exist_ok=True)
    
    clean_name = "Reloaded_installer_1_4Yn174EAEwk"
    output_file = dst_folder / (clean_name + ".h")
    
    print(f"Processing {file_path} -> {output_file}")
    
    # Process file
    melody_type = process_audio_file(file_path, output_file, max_duration=30.0, note_duration=0.2, max_notes=50)
    print(f"Result: {melody_type}")

if __name__ == "__main__":
    process_single_file()
