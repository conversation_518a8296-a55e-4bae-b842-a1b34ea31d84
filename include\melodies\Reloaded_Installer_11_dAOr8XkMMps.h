// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #11 [dAOr8XkMMps].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_11_dAOr8XkMMps_note_count = 50;
constexpr int Reloaded_Installer_11_dAOr8XkMMps_frequencies[] = {
    0, 165, 169, 189, 506, 218, 212, 213, 
    319, 171, 507, 189, 378, 511, 644, 159, 
    212, 213, 165, 169, 188, 184, 1021, 212, 
    212, 214, 170, 572, 189, 481, 179, 212, 
    212, 212, 212, 167, 169, 188, 182, 213, 
    212, 251, 251, 170, 252, 191, 181, 188, 
    212, 212
};
constexpr int Reloaded_Installer_11_dAOr8XkMMps_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_11_dAOr8XkMMps() {
    for (int i = 0; i < Reloaded_Installer_11_dAOr8XkMMps_note_count; i++) {
        if (Reloaded_Installer_11_dAOr8XkMMps_frequencies[i] > 0) {
            beep(Reloaded_Installer_11_dAOr8XkMMps_frequencies[i], Reloaded_Installer_11_dAOr8XkMMps_durations[i]);
        } else {
            delay(Reloaded_Installer_11_dAOr8XkMMps_durations[i]); // Rest (silence)
        }
    }
}
