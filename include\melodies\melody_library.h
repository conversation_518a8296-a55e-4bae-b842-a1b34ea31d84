// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

// Include all melody headers
#include "Reloaded_installer_1_4Yn174EAEwk.h"
#include "Reloaded_Installer_10_ANDQoEl2StM.h"
#include "Reloaded_Installer_11_dAOr8XkMMps.h"
#include "Reloaded_Installer_12_bFBgHoUtCQ8.h"
#include "Reloaded_Installer_13_zkeJ6Cko8pU.h"
#include "Reloaded_Installer_14_xKVjR5wJZK8.h"
#include "Reloaded_Installer_15_v7D24vz0c1o.h"
#include "Reloaded_Installer_16_o_y5InuaeQk.h"
#include "Reloaded_Installer_17_SnDYXnDKJ2M.h"
#include "Reloaded_Installer_2_765KSJ95GUg.h"
#include "Reloaded_Installer_3_7VkqerXVtTA.h"
#include "Reloaded_Installer_4_AZxYFcaDiuM.h"
#include "Reloaded_Installer_5__A3zJLXLjHU.h"
#include "Reloaded_Installer_6_XgL3RMNhE_I.h"
#include "Reloaded_Installer_7_WnBQvgjRWy8.h"
#include "Reloaded_Installer_8_muMeSSrWSrc.h"
#include "Reloaded_Installer_9__o7WvGCq3FQ.h"

// Melody enumeration
enum MelodyId {
    MELODY_RELOADED_INSTALLER_1_4YN174EAEWK,
    MELODY_RELOADED_INSTALLER_10_ANDQOEL2STM,
    MELODY_RELOADED_INSTALLER_11_DAOR8XKMMPS,
    MELODY_RELOADED_INSTALLER_12_BFBGHOUTCQ8,
    MELODY_RELOADED_INSTALLER_13_ZKEJ6CKO8PU,
    MELODY_RELOADED_INSTALLER_14_XKVJR5WJZK8,
    MELODY_RELOADED_INSTALLER_15_V7D24VZ0C1O,
    MELODY_RELOADED_INSTALLER_16_O_Y5INUAEQK,
    MELODY_RELOADED_INSTALLER_17_SNDYXNDKJ2M,
    MELODY_RELOADED_INSTALLER_2_765KSJ95GUG,
    MELODY_RELOADED_INSTALLER_3_7VKQERXVTTA,
    MELODY_RELOADED_INSTALLER_4_AZXYFCADIUM,
    MELODY_RELOADED_INSTALLER_5__A3ZJLXLJHU,
    MELODY_RELOADED_INSTALLER_6_XGL3RMNHE_I,
    MELODY_RELOADED_INSTALLER_7_WNBQVGJRWY8,
    MELODY_RELOADED_INSTALLER_8_MUMESSRWSRC,
    MELODY_RELOADED_INSTALLER_9__O7WVGCQ3FQ
};

// Melody info structure
struct MelodyInfo {
    const int* frequencies;
    const int* durations;
    int note_count;
    void (*play_function)();
};

// Array of all melodies
constexpr MelodyInfo melodies[] = {
    { Reloaded_installer_1_4Yn174EAEwk_frequencies, Reloaded_installer_1_4Yn174EAEwk_durations, Reloaded_installer_1_4Yn174EAEwk_note_count, play_Reloaded_installer_1_4Yn174EAEwk },
    { Reloaded_Installer_10_ANDQoEl2StM_frequencies, Reloaded_Installer_10_ANDQoEl2StM_durations, Reloaded_Installer_10_ANDQoEl2StM_note_count, play_Reloaded_Installer_10_ANDQoEl2StM },
    { Reloaded_Installer_11_dAOr8XkMMps_frequencies, Reloaded_Installer_11_dAOr8XkMMps_durations, Reloaded_Installer_11_dAOr8XkMMps_note_count, play_Reloaded_Installer_11_dAOr8XkMMps },
    { Reloaded_Installer_12_bFBgHoUtCQ8_frequencies, Reloaded_Installer_12_bFBgHoUtCQ8_durations, Reloaded_Installer_12_bFBgHoUtCQ8_note_count, play_Reloaded_Installer_12_bFBgHoUtCQ8 },
    { Reloaded_Installer_13_zkeJ6Cko8pU_frequencies, Reloaded_Installer_13_zkeJ6Cko8pU_durations, Reloaded_Installer_13_zkeJ6Cko8pU_note_count, play_Reloaded_Installer_13_zkeJ6Cko8pU },
    { Reloaded_Installer_14_xKVjR5wJZK8_frequencies, Reloaded_Installer_14_xKVjR5wJZK8_durations, Reloaded_Installer_14_xKVjR5wJZK8_note_count, play_Reloaded_Installer_14_xKVjR5wJZK8 },
    { Reloaded_Installer_15_v7D24vz0c1o_frequencies, Reloaded_Installer_15_v7D24vz0c1o_durations, Reloaded_Installer_15_v7D24vz0c1o_note_count, play_Reloaded_Installer_15_v7D24vz0c1o },
    { Reloaded_Installer_16_o_y5InuaeQk_frequencies, Reloaded_Installer_16_o_y5InuaeQk_durations, Reloaded_Installer_16_o_y5InuaeQk_note_count, play_Reloaded_Installer_16_o_y5InuaeQk },
    { Reloaded_Installer_17_SnDYXnDKJ2M_frequencies, Reloaded_Installer_17_SnDYXnDKJ2M_durations, Reloaded_Installer_17_SnDYXnDKJ2M_note_count, play_Reloaded_Installer_17_SnDYXnDKJ2M },
    { Reloaded_Installer_2_765KSJ95GUg_frequencies, Reloaded_Installer_2_765KSJ95GUg_durations, Reloaded_Installer_2_765KSJ95GUg_note_count, play_Reloaded_Installer_2_765KSJ95GUg },
    { Reloaded_Installer_3_7VkqerXVtTA_frequencies, Reloaded_Installer_3_7VkqerXVtTA_durations, Reloaded_Installer_3_7VkqerXVtTA_note_count, play_Reloaded_Installer_3_7VkqerXVtTA },
    { Reloaded_Installer_4_AZxYFcaDiuM_frequencies, Reloaded_Installer_4_AZxYFcaDiuM_durations, Reloaded_Installer_4_AZxYFcaDiuM_note_count, play_Reloaded_Installer_4_AZxYFcaDiuM },
    { Reloaded_Installer_5__A3zJLXLjHU_frequencies, Reloaded_Installer_5__A3zJLXLjHU_durations, Reloaded_Installer_5__A3zJLXLjHU_note_count, play_Reloaded_Installer_5__A3zJLXLjHU },
    { Reloaded_Installer_6_XgL3RMNhE_I_frequencies, Reloaded_Installer_6_XgL3RMNhE_I_durations, Reloaded_Installer_6_XgL3RMNhE_I_note_count, play_Reloaded_Installer_6_XgL3RMNhE_I },
    { Reloaded_Installer_7_WnBQvgjRWy8_frequencies, Reloaded_Installer_7_WnBQvgjRWy8_durations, Reloaded_Installer_7_WnBQvgjRWy8_note_count, play_Reloaded_Installer_7_WnBQvgjRWy8 },
    { Reloaded_Installer_8_muMeSSrWSrc_frequencies, Reloaded_Installer_8_muMeSSrWSrc_durations, Reloaded_Installer_8_muMeSSrWSrc_note_count, play_Reloaded_Installer_8_muMeSSrWSrc },
    { Reloaded_Installer_9__o7WvGCq3FQ_frequencies, Reloaded_Installer_9__o7WvGCq3FQ_durations, Reloaded_Installer_9__o7WvGCq3FQ_note_count, play_Reloaded_Installer_9__o7WvGCq3FQ }
};

// Helper function to play a melody by ID
void playMelody(MelodyId melody_id) {
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {
        melodies[melody_id].play_function();
    }
}

// Helper function to get melody info
const MelodyInfo& getMelodyInfo(MelodyId melody_id) {
    static const MelodyInfo empty_melody = {nullptr, nullptr, 0, nullptr};
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {
        return melodies[melody_id];
    }
    return empty_melody;
}
