// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #15 [v7D24vz0c1o].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_15_v7D24vz0c1o_note_count = 50;
constexpr int Reloaded_Installer_15_v7D24vz0c1o_frequencies[] = {
    0, 415, 206, 348, 311, 348, 344, 464, 
    415, 419, 412, 312, 370, 232, 343, 270, 
    365, 279, 346, 415, 411, 412, 471, 350, 
    517, 466, 352, 344, 311, 311, 280, 232, 
    467, 363, 311, 233, 414, 524, 415, 172, 
    699, 367, 348, 278, 232, 551, 310, 411, 
    346, 466
};
constexpr int Reloaded_Installer_15_v7D24vz0c1o_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_15_v7D24vz0c1o() {
    for (int i = 0; i < Reloaded_Installer_15_v7D24vz0c1o_note_count; i++) {
        if (Reloaded_Installer_15_v7D24vz0c1o_frequencies[i] > 0) {
            beep(Reloaded_Installer_15_v7D24vz0c1o_frequencies[i], Reloaded_Installer_15_v7D24vz0c1o_durations[i]);
        } else {
            delay(Reloaded_Installer_15_v7D24vz0c1o_durations[i]); // Rest (silence)
        }
    }
}
