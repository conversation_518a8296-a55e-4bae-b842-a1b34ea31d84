// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #4 [AZxYFcaDiuM].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_4_AZxYFcaDiuM_note_count = 50;
constexpr int Reloaded_Installer_4_AZxYFcaDiuM_frequencies[] = {
    0, 166, 168, 405, 150, 152, 300, 150, 
    304, 168, 510, 1023, 259, 158, 149, 149, 
    149, 149, 167, 168, 681, 147, 151, 300, 
    149, 383, 169, 511, 1010, 904, 159, 150, 
    149, 149, 766, 168, 168, 403, 410, 150, 
    149, 150, 200, 168, 340, 200, 266, 169, 
    300, 149
};
constexpr int Reloaded_Installer_4_AZxYFcaDiuM_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_4_AZxYFcaDiuM() {
    for (int i = 0; i < Reloaded_Installer_4_AZxYFcaDiuM_note_count; i++) {
        if (Reloaded_Installer_4_AZxYFcaDiuM_frequencies[i] > 0) {
            beep(Reloaded_Installer_4_AZxYFcaDiuM_frequencies[i], Reloaded_Installer_4_AZxYFcaDiuM_durations[i]);
        } else {
            delay(Reloaded_Installer_4_AZxYFcaDiuM_durations[i]); // Rest (silence)
        }
    }
}
