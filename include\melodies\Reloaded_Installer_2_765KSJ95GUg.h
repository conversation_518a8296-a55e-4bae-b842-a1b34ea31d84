// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #2 [765KSJ95GUg].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_2_765KSJ95GUg_note_count = 50;
constexpr int Reloaded_Installer_2_765KSJ95GUg_frequencies[] = {
    0, 297, 606, 199, 168, 573, 455, 252, 
    149, 251, 150, 200, 200, 163, 225, 508, 
    252, 224, 297, 606, 199, 168, 453, 680, 
    251, 147, 251, 150, 200, 201, 163, 225, 
    1012, 252, 221, 764, 251, 200, 167, 763, 
    678, 251, 147, 251, 149, 200, 200, 161, 
    225, 760
};
constexpr int Reloaded_Installer_2_765KSJ95GUg_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_2_765KSJ95GUg() {
    for (int i = 0; i < Reloaded_Installer_2_765KSJ95GUg_note_count; i++) {
        if (Reloaded_Installer_2_765KSJ95GUg_frequencies[i] > 0) {
            beep(Reloaded_Installer_2_765KSJ95GUg_frequencies[i], Reloaded_Installer_2_765KSJ95GUg_durations[i]);
        } else {
            delay(Reloaded_Installer_2_765KSJ95GUg_durations[i]); // Rest (silence)
        }
    }
}
