// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #5 [-A3zJLXLjHU].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_5__A3zJLXLjHU_note_count = 50;
constexpr int Reloaded_Installer_5__A3zJLXLjHU_frequencies[] = {
    0, 188, 188, 189, 450, 168, 225, 170, 
    339, 572, 186, 377, 0, 379, 171, 450, 
    169, 212, 429, 188, 189, 453, 338, 453, 
    436, 348, 380, 450, 575, 454, 679, 462, 
    452, 168, 346, 569, 188, 189, 679, 455, 
    679, 424, 353, 380, 187, 570, 454, 679, 
    459, 452
};
constexpr int Reloaded_Installer_5__A3zJLXLjHU_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_5__A3zJLXLjHU() {
    for (int i = 0; i < Reloaded_Installer_5__A3zJLXLjHU_note_count; i++) {
        if (Reloaded_Installer_5__A3zJLXLjHU_frequencies[i] > 0) {
            beep(Reloaded_Installer_5__A3zJLXLjHU_frequencies[i], Reloaded_Installer_5__A3zJLXLjHU_durations[i]);
        } else {
            delay(Reloaded_Installer_5__A3zJLXLjHU_durations[i]); // Rest (silence)
        }
    }
}
