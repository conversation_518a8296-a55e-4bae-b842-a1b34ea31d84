// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #16 [o_y5InuaeQk].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_16_o_y5InuaeQk_note_count = 50;
constexpr int Reloaded_Installer_16_o_y5InuaeQk_frequencies[] = {
    0, 643, 507, 855, 212, 763, 187, 509, 
    570, 639, 965, 158, 158, 764, 189, 680, 
    860, 764, 508, 858, 860, 859, 1139, 189, 
    1020, 762, 1275, 211, 859, 852, 967, 189, 
    190, 509, 767, 848, 862, 864, 212, 189, 
    189, 1017, 961, 852, 863, 636, 212, 1012, 
    959, 962
};
constexpr int Reloaded_Installer_16_o_y5InuaeQk_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_16_o_y5InuaeQk() {
    for (int i = 0; i < Reloaded_Installer_16_o_y5InuaeQk_note_count; i++) {
        if (Reloaded_Installer_16_o_y5InuaeQk_frequencies[i] > 0) {
            beep(Reloaded_Installer_16_o_y5InuaeQk_frequencies[i], Reloaded_Installer_16_o_y5InuaeQk_durations[i]);
        } else {
            delay(Reloaded_Installer_16_o_y5InuaeQk_durations[i]); // Rest (silence)
        }
    }
}
