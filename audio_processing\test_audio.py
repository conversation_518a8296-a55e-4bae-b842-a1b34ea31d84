#!/usr/bin/env python3
"""
Test script for audio processing functionality.
"""

from pathlib import Path
from .core import extract_melody_from_audio, simplify_melody, melody_to_cpp_arrays

def test_audio_processing():
    """Test audio processing with a sample MP3 file."""
    src_folder = Path("data-unprocessed")
    
    # Find the first MP3 file
    mp3_files = list(src_folder.glob("*.mp3"))
    if not mp3_files:
        print("No MP3 files found in data-unprocessed folder")
        return
    
    test_file = mp3_files[0]
    print(f"Testing with: {test_file}")
    
    try:
        # Extract melody
        melody = extract_melody_from_audio(test_file, max_duration=10.0, note_duration=0.3)
        print(f"Extracted {len(melody)} notes")
        
        # Show first few notes
        for i, (freq, dur) in enumerate(melody[:5]):
            print(f"  Note {i+1}: {freq:.1f} Hz for {dur:.2f}s")
        
        # Simplify melody
        simplified = simplify_melody(melody, max_notes=20)
        print(f"Simplified to {len(simplified)} notes")
        
        # Convert to C++ arrays
        freq_array, dur_array, count = melody_to_cpp_arrays(simplified, "test_melody")
        print(f"Generated C++ arrays for {count} notes")
        print("Frequency array preview:")
        print(freq_array[:100] + "..." if len(freq_array) > 100 else freq_array)
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_audio_processing()
