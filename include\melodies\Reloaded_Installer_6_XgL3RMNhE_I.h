// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #6 [XgL3RMNhE-I].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_6_XgL3RMNhE_I_note_count = 50;
constexpr int Reloaded_Installer_6_XgL3RMNhE_I_frequencies[] = {
    0, 506, 569, 383, 188, 212, 679, 571, 
    168, 381, 642, 572, 188, 506, 212, 341, 
    647, 188, 506, 570, 186, 189, 212, 680, 
    571, 168, 380, 252, 572, 188, 506, 213, 
    342, 571, 189, 508, 386, 387, 481, 508, 
    429, 514, 168, 507, 251, 571, 189, 483, 
    212, 429
};
constexpr int Reloaded_Installer_6_XgL3RMNhE_I_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_6_XgL3RMNhE_I() {
    for (int i = 0; i < Reloaded_Installer_6_XgL3RMNhE_I_note_count; i++) {
        if (Reloaded_Installer_6_XgL3RMNhE_I_frequencies[i] > 0) {
            beep(Reloaded_Installer_6_XgL3RMNhE_I_frequencies[i], Reloaded_Installer_6_XgL3RMNhE_I_durations[i]);
        } else {
            delay(Reloaded_Installer_6_XgL3RMNhE_I_durations[i]); // Rest (silence)
        }
    }
}
