// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

// Include all melody headers
{includes}

// Melody enumeration
enum MelodyId {{
{enum_entries}
}};

// Melody info structure
struct MelodyInfo {{
    const int* frequencies;
    const int* durations;
    int note_count;
    void (*play_function)();
}};

// Array of all melodies
constexpr MelodyInfo melodies[] = {{
{melody_entries}
}};

// Helper function to play a melody by ID
void playMelody(MelodyId melody_id) {{
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {{
        melodies[melody_id].play_function();
    }}
}}

// Helper function to get melody info
const MelodyInfo& getMelodyInfo(MelodyId melody_id) {{
    static const MelodyInfo empty_melody = {{nullptr, nullptr, 0, nullptr}};
    if (melody_id >= 0 && melody_id < sizeof(melodies) / sizeof(melodies[0])) {{
        return melodies[melody_id];
    }}
    return empty_melody;
}}
