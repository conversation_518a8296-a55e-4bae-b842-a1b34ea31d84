// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #10 [ANDQoEl2StM].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_10_ANDQoEl2StM_note_count = 50;
constexpr int Reloaded_Installer_10_ANDQoEl2StM_frequencies[] = {
    0, 147, 149, 150, 721, 152, 150, 150, 
    150, 482, 481, 474, 237, 538, 267, 267, 
    267, 539, 147, 150, 149, 909, 151, 149, 
    150, 150, 177, 481, 472, 237, 187, 267, 
    267, 721, 159, 149, 151, 149, 458, 150, 
    150, 151, 149, 456, 451, 453, 236, 539, 
    268, 341
};
constexpr int Reloaded_Installer_10_ANDQoEl2StM_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_10_ANDQoEl2StM() {
    for (int i = 0; i < Reloaded_Installer_10_ANDQoEl2StM_note_count; i++) {
        if (Reloaded_Installer_10_ANDQoEl2StM_frequencies[i] > 0) {
            beep(Reloaded_Installer_10_ANDQoEl2StM_frequencies[i], Reloaded_Installer_10_ANDQoEl2StM_durations[i]);
        } else {
            delay(Reloaded_Installer_10_ANDQoEl2StM_durations[i]); // Rest (silence)
        }
    }
}
