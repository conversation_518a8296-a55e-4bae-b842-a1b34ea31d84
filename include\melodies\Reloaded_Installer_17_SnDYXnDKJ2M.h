// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #17 [SnDYXnDKJ2M].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_17_SnDYXnDKJ2M_note_count = 50;
constexpr int Reloaded_Installer_17_SnDYXnDKJ2M_frequencies[] = {
    0, 482, 580, 570, 548, 286, 482, 569, 
    651, 637, 728, 719, 179, 716, 647, 478, 
    179, 287, 583, 569, 547, 484, 480, 569, 
    571, 319, 179, 0, 179, 0, 159, 538, 
    178, 286, 0, 479, 267, 569, 179, 0, 
    570, 319, 179, 720, 642, 571, 429, 572, 
    179, 720
};
constexpr int Reloaded_Installer_17_SnDYXnDKJ2M_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_17_SnDYXnDKJ2M() {
    for (int i = 0; i < Reloaded_Installer_17_SnDYXnDKJ2M_note_count; i++) {
        if (Reloaded_Installer_17_SnDYXnDKJ2M_frequencies[i] > 0) {
            beep(Reloaded_Installer_17_SnDYXnDKJ2M_frequencies[i], Reloaded_Installer_17_SnDYXnDKJ2M_durations[i]);
        } else {
            delay(Reloaded_Installer_17_SnDYXnDKJ2M_durations[i]); // Rest (silence)
        }
    }
}
