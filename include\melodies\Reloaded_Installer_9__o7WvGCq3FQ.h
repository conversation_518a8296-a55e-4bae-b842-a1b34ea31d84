// This file is auto-generated by preprocess_audio_files.py. Do not edit this manually.
#pragma once

/*
Converted from Reloaded Installer #9 [-o7WvGCq3FQ].mp3 - 50 notes, 30.0s max duration
Note duration: 0.2s per note
Max duration: 30.0s
*/

constexpr int Reloaded_Installer_9__o7WvGCq3FQ_note_count = 50;
constexpr int Reloaded_Installer_9__o7WvGCq3FQ_frequencies[] = {
    0, 340, 254, 303, 301, 302, 381, 254, 
    255, 202, 254, 404, 256, 302, 226, 302, 
    396, 340, 340, 256, 250, 303, 300, 381, 
    254, 255, 202, 255, 406, 382, 302, 226, 
    452, 382, 340, 761, 515, 250, 507, 376, 
    509, 382, 189, 150, 809, 817, 761, 459, 
    680, 452
};
constexpr int Reloaded_Installer_9__o7WvGCq3FQ_durations[] = {
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200, 200, 200, 200, 200, 200, 200, 
    200, 200
};

// Helper function to play this melody
void play_Reloaded_Installer_9__o7WvGCq3FQ() {
    for (int i = 0; i < Reloaded_Installer_9__o7WvGCq3FQ_note_count; i++) {
        if (Reloaded_Installer_9__o7WvGCq3FQ_frequencies[i] > 0) {
            beep(Reloaded_Installer_9__o7WvGCq3FQ_frequencies[i], Reloaded_Installer_9__o7WvGCq3FQ_durations[i]);
        } else {
            delay(Reloaded_Installer_9__o7WvGCq3FQ_durations[i]); // Rest (silence)
        }
    }
}
